"""
Unified session management for both FSM sequences and backend operations.
Handles session creation, validation, state management across the entire system.
"""

import asyncio
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from managers.logger_manager import logger
from pydantic import BaseModel

INITIAL_CONNECTION_TIMEOUT_SECONDS = 20  # Time to wait for initial WebSocket connection after session creation
RECONNECTION_TIMEOUT_SECONDS = 10        # Time to wait for reconnection after WebSocket disconnect

class SessionType:
    """Session type constants"""
    FSM_SEQUENCE = "fsm_sequence"
    BACKEND_SALE = "backend_sale"
    BACKEND_PICKUP = "backend_pickup"
    BACKEND_STORAGE = "backend_storage"
    PAYMENT = "payment"  # Deprecated - replaced by TRANSACTION
    TRANSACTION = "transaction"  # Universal transaction session (payment, storage, etc.)
    PRODUCT_FLOW = "product_flow"  # New flow-based product sessions
    STORAGE_FLOW = "storage_flow"  # New flow-based storage sessions
    ORDER_FLOW = "order_flow"  # New flow-based order sessions
    OPERATOR_FSM = "operator_fsm"  # Operator FSM sequences (no door closing required)
    STORAGE_PICKUP = "storage_pickup"  # Simple storage pickup using pickup_process directly
    PRODUCT_PICKUP = "product_pickup"  # Simple product pickup using pickup_process directly

class SessionStatus:
    """Session status constants"""
    PENDING = "pending"
    ACTIVE = "active"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"

class SectionConfig(BaseModel):
    """Configuration for a locker section"""
    section_id: int          # Visual ID shown to user (e.g., "Section 1")
    lock_id: int            # Actual hardware lock pin/output for unlock/lock commands  
    is_tempered: bool
    led_section: Optional[int] = None  # LED ID for this section

class SessionData(BaseModel):
    """Session data model"""
    session_id: str
    session_type: str
    status: str = SessionStatus.PENDING
    created_at: datetime
    expires_at: Optional[datetime] = None

    # FSM specific data
    pin: Optional[str] = None
    sections: Optional[List[SectionConfig]] = None
    current_section_index: Optional[int] = None
    auto_mode: bool = False
    log_pin: Optional[str] = None       # PIN logged in the timeline

    # Backend specific data
    user_data: Optional[Dict[str, Any]] = None          #delete after
    transaction_data: Optional[Dict[str, Any]] = None
    
    # Transaction specific data
    amount: Optional[float] = None

    # Storage flow specific data
    operation: Optional[str] = None
    section_id: Optional[int] = None
    size_category: Optional[int] = None
    reservation_id: Optional[int] = None
    reservation_pin: Optional[str] = None
    email: Optional[str] = None
    storage_deactivate_reservation: bool = True  # Track if reservation should be deactivated
    max_storage_hours: Optional[int] = None
    storage_expiration_time_str: Optional[str] = None   # expiration time in format 22.09.2025 10:00:00

    # Process state tracking for reconnection continuity
    current_step: Optional[str] = None  # Track current step: "payment", "hardware", "completed"
    payment_completed: bool = False  # Track if payment step was completed

    # WebSocket connection tracking
    first_connected: bool = False  # Track if WebSocket has ever connected
    last_disconnected_at: Optional[datetime] = None  # Track when WebSocket last disconnected
    connection_timeout_task: Optional[Any] = None  # Async task for timeout handling

    # Endpoint tracking - which endpoint started the FSM sequence
    endpoint_type: Optional[str] = None  # 'product', 'storage', 'order', 'operator', etc.

    # Product flow specific data
    product_id: Optional[int] = None
    product_uuid: Optional[str] = None

    # Order flow specific data
    operator_id: Optional[str] = None
    phone_number: Optional[str] = None
    pickup_pin: Optional[str] = None
    insert_pin: Optional[str] = None
    deliver_checked_from_server: bool = False  # Track if delivery was validated by server
    age_control_required: bool = False  # Track if age control is required for this session
    age_controlled: bool = False  # Track if age control was completed for this session
    reservations_info: Optional[List[Dict[str, Any]]] = None  # Reservation info for order flow sessions
    order_number: Optional[str] = None  # Order number for order flow sessions
    order_uuid: Optional[str] = None  # Order UUID for order flow sessions

    # MAGNA
    reserved_sections: Optional[List[int]] = []  # Sections reserved by the user

    class Config:
        arbitrary_types_allowed = True

class SessionManager:
    """Unified session manager"""
    
    def __init__(self):
        self.active_sessions: Dict[str, SessionData] = {}
        self.default_expiry_minutes = 30
        self.cleanup_task = None
        
    async def start_cleanup_task(self):
        """Start background cleanup task for expired sessions"""
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_expired_sessions())
    
    async def stop_cleanup_task(self):
        """Stop background cleanup task"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
            self.cleanup_task = None
    
    async def _cleanup_expired_sessions(self):
        """Background task to clean up expired sessions"""
        while True:
            try:
                now = datetime.now()
                expired_sessions = []
                
                for session_id, session in self.active_sessions.items():
                    if session.expires_at and now > session.expires_at:
                        expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    logger.info(f"Cleaning up expired session: {session_id}")
                    await self.remove_session(session_id)
                
                # Clean up every 5 minutes
                await asyncio.sleep(300)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying
    
    def create_session(
        self, 
        session_type: str,
        expiry_minutes: Optional[int] = None,
        session_id: Optional[str] = None,
        **kwargs
    ) -> SessionData:
        """
        Create a new session
        
        Args:
            session_type: Type of session (FSM_SEQUENCE, BACKEND_SALE, etc.)
            expiry_minutes: Session expiry in minutes (default: 30)
            session_id: Optional custom session ID (if not provided, will generate UUID)
            **kwargs: Additional session data
            
        Returns:
            SessionData object
        """
        if not session_id:
            session_id = str(uuid.uuid4())
            
        now = datetime.now()
        expiry = now + timedelta(minutes=expiry_minutes or self.default_expiry_minutes)
        
        session = SessionData(
            session_id=session_id,
            session_type=session_type,
            created_at=now,
            expires_at=expiry,
            **kwargs
        )
        
        self.active_sessions[session_id] = session
        logger.info(f"Created {session_type} session: {session_id}")

        # Start initial connection timeout
        self.start_initial_connection_timeout(session_id)

        return session
    
    def get_session(self, session_id: str) -> Optional[SessionData]:
        """Get session by ID"""
        session = self.active_sessions.get(session_id)
        
        if session and session.expires_at and datetime.now() > session.expires_at:
            logger.warning(f"Session {session_id} has expired")
            self.active_sessions.pop(session_id, None)
            return None
            
        return session
    
    def update_session(self, session_id: str, **updates) -> bool:
        """Update session data"""
        session = self.get_session(session_id)
        if not session:
            return False
        
        for key, value in updates.items():
            if hasattr(session, key):
                setattr(session, key, value)
            else:
                logger.warning(f"Unknown session attribute: {key}")
        
        logger.info(f"Updated session {session_id}: {updates}")
        return True
    
    async def remove_session(self, session_id: str) -> bool:
        """Remove session"""
        if session_id in self.active_sessions:
            session = self.active_sessions.pop(session_id)

            # Cancel any active timeout task
            if session.connection_timeout_task:
                session.connection_timeout_task.cancel()
                session.connection_timeout_task = None

            logger.info(f"Removed session: {session_id} (type: {session.session_type})")
            return True
        return False
    
    def get_sessions_by_type(self, session_type: str) -> List[SessionData]:
        """Get all sessions of specific type"""
        return [
            session for session in self.active_sessions.values()
            if session.session_type == session_type
        ]
    
    def extend_session(self, session_id: str, minutes: int = 30) -> bool:
        """Extend session expiry time"""
        session = self.get_session(session_id)
        if not session:
            return False

        session.expires_at = datetime.now() + timedelta(minutes=minutes)
        logger.info(f"Extended session {session_id} by {minutes} minutes")
        return True

    async def _timeout_session(self, session_id: str, reason: str):
        """Internal method to timeout and remove a session"""
        logger.warning(f"SESSION TIMEOUT: {session_id} - {reason}")
        await self.remove_session(session_id)

    def start_initial_connection_timeout(self, session_id: str):
        """Start timeout for initial WebSocket connection"""
        session = self.get_session(session_id)
        if not session:
            return

        async def timeout_task():
            await asyncio.sleep(INITIAL_CONNECTION_TIMEOUT_SECONDS)
            session = self.get_session(session_id)
            if session and not session.first_connected:
                await self._timeout_session(session_id, f"No initial connection within {INITIAL_CONNECTION_TIMEOUT_SECONDS} seconds")

        # Cancel existing timeout task if any
        if session.connection_timeout_task:
            session.connection_timeout_task.cancel()

        session.connection_timeout_task = asyncio.create_task(timeout_task())
        logger.info(f"Started initial connection timeout for session {session_id} ({INITIAL_CONNECTION_TIMEOUT_SECONDS}s)")

    def start_reconnection_timeout(self, session_id: str):
        """Start timeout for WebSocket reconnection"""
        session = self.get_session(session_id)
        if not session:
            return

        session.last_disconnected_at = datetime.now()

        async def timeout_task():
            await asyncio.sleep(RECONNECTION_TIMEOUT_SECONDS)
            session = self.get_session(session_id)
            if session and session.last_disconnected_at:
                await self._timeout_session(session_id, f"No reconnection within {RECONNECTION_TIMEOUT_SECONDS} seconds")

        # Cancel existing timeout task if any
        if session.connection_timeout_task:
            session.connection_timeout_task.cancel()

        session.connection_timeout_task = asyncio.create_task(timeout_task())
        logger.info(f"Started reconnection timeout for session {session_id} ({RECONNECTION_TIMEOUT_SECONDS}s)")

    def mark_connected(self, session_id: str):
        """Mark session as connected and cancel any timeout tasks"""
        session = self.get_session(session_id)
        if not session:
            return

        # Cancel timeout task
        if session.connection_timeout_task:
            session.connection_timeout_task.cancel()
            session.connection_timeout_task = None

        # Mark as connected
        session.first_connected = True
        session.last_disconnected_at = None

        logger.info(f"Session {session_id} marked as connected (timeout cancelled)")

# Global session manager instance
session_manager = SessionManager()
