import mysql.connector
from os import getenv
from typing import List, Dict, Any, Optional
from uuid import uuid4
from managers.logger_manager import logger

from managers.logger_manager import logger

def get_db():
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

class OrderRepository:
    """Repository for order database operations"""
    
    def __init__(self):
        self.logger = logger
        

    
    def get_expired_order_sections(self) -> List[int]:
        """
        Get all sections with expired orders.
        Returns all section_ids from order_reservations where expired=0 and status=1 and type='employee_send'
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id 
                FROM order_reservations 
                WHERE expired = 0 AND status = 1 AND type = 'employee_send'
                ORDER BY section_id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row['section_id'] for row in results]
        except Exception as e:
            self.logger.error(f"Error getting expired order sections: {e}")
            return []
        finally:
            cursor.close()
            db.close()
    
    def get_employee_order_sections(self) -> List[int]:
        """
        Get all sections with employee orders.
        Returns all section_ids from order_reservations where expired=0 and status=1 and type='employee_send'
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT DISTINCT CAST(section_id AS UNSIGNED) as section_id 
                FROM order_reservations 
                WHERE expired = 0 AND status = 1 AND type = 'employee_send'
                ORDER BY section_id
            """
            cursor.execute(query)
            results = cursor.fetchall()
            return [row['section_id'] for row in results]
        except Exception as e:
            self.logger.error(f"Error getting employee order sections: {e}")
            return []
        finally:
            cursor.close()
            db.close()
    
    def create_employee_delivery_reservation(self, phone_number: str, section_id: int) -> Dict[str, Any]:
        """
        Create a new order reservation for employee delivery.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate unique UUID and PIN
            reservation_uuid = str(uuid4())
            from .pin_generator import generate_pin
            pickup_pin = generate_pin()
            
            if pickup_pin is None:
                return {"success": False, "error": "Failed to generate unique PIN"}
            
            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")
            
            query = """
                INSERT INTO order_reservations 
                (uuid, box_uuid, section_id, status, pickup_pin, phone_number, expired, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, 0, 'employee_deliver', NOW(), NOW())
            """
            cursor.execute(query, (reservation_uuid, box_uuid, str(section_id), pickup_pin, phone_number))
            db.commit()
            
            return {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "pickup_pin": pickup_pin,
                "section_id": section_id
            }
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating employee delivery reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()
    
    def create_employee_send_reservation(self, phone_number: str, section_id: int) -> Dict[str, Any]:
        """
        Create a new order reservation for employee sending order.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate unique UUID and PIN
            reservation_uuid = str(uuid4())
            from .pin_generator import generate_pin
            insert_pin = generate_pin()
            
            if insert_pin is None:
                return {"success": False, "error": "Failed to generate unique PIN"}
            
            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")
            
            query = """
                INSERT INTO order_reservations 
                (uuid, box_uuid, section_id, status, insert_pin, phone_number, expired, type, created_at, last_update)
                VALUES (%s, %s, %s, 1, %s, %s, 0, 'employee_send', NOW(), NOW())
            """
            cursor.execute(query, (reservation_uuid, box_uuid, str(section_id), insert_pin, phone_number))
            db.commit()
            
            return {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "insert_pin": insert_pin,
                "section_id": section_id
            }
        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating employee send reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()
    



    def create_order_reservation(self, phone_number: str = None, section_id: int = None, status: int = None,
                                 lookup_pin: str = None, insert_pin: str = None, pickup_pin: str = None,
                                 auto_generate_pin: bool = True) -> Dict[str, Any]:
        """
        Unified method to create order reservations with flexible parameters.

        Args:
            phone_number: Phone number for the reservation (required unless lookup_pin is provided)
            section_id: Section ID for the reservation
            status: Status code for the reservation
            lookup_pin: PIN to lookup existing reservation for phone number (for customer_send operations)
            insert_pin: Specific insert PIN to use (overrides auto-generation)
            pickup_pin: Specific pickup PIN to use
            auto_generate_pin: Whether to auto-generate insert_pin if not provided

        Returns:
            Dict with success status, reservation details, and any errors
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # If lookup_pin is provided, find phone number from existing reservation
            if lookup_pin and not phone_number:
                cursor.execute("""
                    SELECT phone_number FROM order_reservations
                    WHERE insert_pin = %s OR pickup_pin = %s
                    ORDER BY created_at DESC LIMIT 1
                """, (lookup_pin, lookup_pin))

                existing_reservation = cursor.fetchone()
                if not existing_reservation:
                    return {"success": False, "error": "No reservation found with this PIN"}

                phone_number = existing_reservation['phone_number']

            # Validate required parameters
            if not phone_number or section_id is None or status is None:
                return {"success": False, "error": "Missing required parameters: phone_number, section_id, status"}

            # Generate unique UUID
            reservation_uuid = str(uuid4())

            # Handle insert_pin generation/assignment
            self.logger.info(f"create_order_reservation called with: phone_number={phone_number}, section_id={section_id}, status={status}, insert_pin='{insert_pin}', pickup_pin='{pickup_pin}', auto_generate_pin={auto_generate_pin}")

            # For reclaim operations (status=3), never auto-generate insert_pin
            if status == 3:
                self.logger.info(f"Reclaim operation detected (status=3), using provided insert_pin: '{insert_pin}' without auto-generation")
            elif auto_generate_pin and not insert_pin:
                from .pin_generator import generate_pin
                insert_pin = generate_pin()
                self.logger.info(f"Generated new insert_pin: '{insert_pin}'")
                if insert_pin is None:
                    return {"success": False, "error": "Failed to generate unique PIN"}
            else:
                self.logger.info(f"Using provided insert_pin: '{insert_pin}' (auto_generate_pin={auto_generate_pin})")

            # Get box_uuid from environment
            box_uuid = getenv("BOX_UUID", "default-box")

            # Determine type based on status
            type_mapping = {
                1: "delivered",
                2: "employee_send",
                3: "customer_reclaim",
                4: "customer_send"
            }
            reservation_type = type_mapping.get(status, "unknown")

            # Build query dynamically based on available fields
            fields = ["uuid", "box_uuid", "section_id", "status", "phone_number", "expired", "type", "created_at", "last_update"]
            values = [reservation_uuid, box_uuid, str(section_id), status, phone_number, 0, reservation_type, "NOW()", "NOW()"]
            placeholders = ["%s", "%s", "%s", "%s", "%s", "%s", "%s", "NOW()", "NOW()"]

            if insert_pin is not None:
                fields.insert(-3, "insert_pin")  # Insert before created_at, last_update, expired
                values.insert(-3, insert_pin)
                placeholders.insert(-3, "%s")

            if pickup_pin is not None:
                fields.insert(-3, "pickup_pin")  # Insert before created_at, last_update, expired
                values.insert(-3, pickup_pin)
                placeholders.insert(-3, "%s")

            query = f"""
                INSERT INTO order_reservations ({', '.join(fields)})
                VALUES ({', '.join(placeholders)})
            """

            # Remove NOW() from values for execution
            exec_values = [v for v in values if v != "NOW()"]
            cursor.execute(query, exec_values)
            db.commit()

            result = {
                "success": True,
                "reservation_id": cursor.lastrowid,
                "section_id": section_id,
                "uuid": reservation_uuid,
                "phone_number": phone_number
            }

            if insert_pin:
                result["insert_pin"] = insert_pin
            if pickup_pin:
                result["pickup_pin"] = pickup_pin

            self.logger.info(f"Created order reservation: UUID={reservation_uuid}, phone={phone_number}, section={section_id}, status={status}, insert_pin={insert_pin}, pickup_pin={pickup_pin}")
            return result

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating order reservation: {e}")
            return {"success": False, "error": str(e)}
        finally:
            cursor.close()
            db.close()












    async def handle_section_open(self, session_id: str, section_id: int, endpoint_type: str) -> bool:
        """Thuis function is called in pickup_process() after unlocking each seciton."""
        match endpoint_type:
            case "order/operator/pickup-expired":
                # TODO: add log_timeline_event()
                result = await self.edit_reservations(find_section_id=section_id, find_status=6, set_status=0)     # deactivate order reservation
                return len(result) > 0
            case "order/operator/pickup":
                # TODO: add log_timeline_event()
                result = await self.edit_reservations(find_section_id=section_id, find_status=8, set_status=0)     # deactivate order reservation
                return len(result) > 0
            case "order/customer/pickup":
                # TODO: add log_timeline_event()
                result = await self.edit_reservations(find_section_id=section_id,  set_status=0)     # deactivate order reservation
                return len(result) > 0
            case "order/operator/check-after-delivery":
                # Change status from 3 to 4 after opening section in pickup process
                result = await self.edit_reservations(find_section_id=section_id, find_status=3, set_status=4)
                return len(result) > 0
            case _:
                logger.info(f"Unknown endpoint type for order handle_section_open: {endpoint_type}")
                return True



    async def create_reservation(
        self,
        order_uuid: str = None,     # if None, it will automaticky generate
        order_number: str = None,
        serial_number: str = None,
        status: int = 1,
        seciton_id: int = None,
        insert_pin: str = None,
        package_pin: str = None,
        pickup_pin: str = None,
        card_nubmer: str = None,
        age_control_required: bool = False,
        age_controlled: bool = 0,
        phone_nubmer: str = None,
        max_days: int = None,       # max days till it reservation expires
        payment_required: bool = False,
        price: float = 0,
        paid_status: bool = 0,      # if order was paid
        expired: int = 0,           # if order is expired
        type: str = None,           # type of order (magna, pradelna, etc.)
        section_can_change: int = 0     # if courier can change the section (0 = no, 1 = yes)
        ) -> Optional[Dict[str, Any]]:

        """
        Function to create new record in table order_reservations.

        Returns:
            Dict[str, Any]: Reservation data if successful, None if failed
        """

        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Generate UUID if not provided
            if order_uuid is None:
                order_uuid = str(uuid4())

            # Generate PINs if not provided
            if insert_pin is None:
                from .pin_generator import generate_pin
                insert_pin = generate_pin()
                if insert_pin is None:
                    self.logger.error("Failed to generate unique insert PIN")
                    return None

            if pickup_pin is None:
                from .pin_generator import generate_pin
                pickup_pin = generate_pin()
                if pickup_pin is None:
                    self.logger.error("Failed to generate unique pickup PIN")
                    return None

            # Build the INSERT query
            query = """
                INSERT INTO order_reservations (
                    order_uuid, order_number, serial_number, status, section_id,
                    insert_pin, package_pin, pickup_pin, card_number,
                    age_control_required, age_controlled, phone_number, max_days,
                    price, payment_required, paid_status, expired, type,
                    sectionc_can_change, created_at, last_update
                ) VALUES (
                    %s, %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s, %s,
                    %s, NOW(), NOW()
                )
            """

            # Execute the query
            cursor.execute(query, (
                order_uuid, order_number, serial_number, status, seciton_id,
                insert_pin, package_pin, pickup_pin, card_nubmer,
                age_control_required, age_controlled, phone_nubmer, max_days,
                price, payment_required, paid_status, expired, type,
                section_can_change
            ))
            db.commit()

            reservation_id = cursor.lastrowid

            # Return success with reservation data
            reservation_data = {
                "id": reservation_id,
                "order_uuid": order_uuid,
                "order_number": order_number,
                "serial_number": serial_number,
                "status": status,
                "section_id": seciton_id,
                "insert_pin": insert_pin,
                "package_pin": package_pin,
                "pickup_pin": pickup_pin,
                "card_number": card_nubmer,
                "age_control_required": age_control_required,
                "age_controlled": age_controlled,
                "phone_number": phone_nubmer,
                "max_days": max_days,
                "price": price,
                "payment_required": payment_required,
                "paid_status": paid_status,
                "expired": expired,
                "type": type,
                "section_can_change": section_can_change
            }

            self.logger.info(f"Created order reservation: ID={reservation_id}, UUID={order_uuid}, phone={phone_nubmer}, section={seciton_id}")
            return reservation_data

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error creating order reservation: {e}")
            return None
        finally:
            cursor.close()
            db.close()






    async def edit_reservations(
        self,

        # Search parameters - can be used to find reservation instead of order_uuid
        reservation_id: Optional[int] = None,
        order_uuid: Optional[str] = None,
        order_number: Optional[str] = None,
        serial_number: Optional[str] = None,
        find_section_id: Optional[int] = None,
        find_status: Optional[int] = 1,
        find_statuses: Optional[List[int]] = None,

        # Update parameters
        set_status: Optional[int] = None,
        set_section_id: Optional[int] = None,
        insert_pin: Optional[str] = None,
        package_pin: Optional[str] = None,
        pickup_pin: Optional[str] = None,
        card_number: Optional[str] = None,
        age_control_required: Optional[bool] = None,
        age_controlled: Optional[bool] = None,
        phone_number: Optional[str] = None,
        price: Optional[float] = None,  # New parameter for price editing
        ) -> List[Dict[str, Any]]:

        """
        Function to edit records in table order_reservations.

        Params:
            reservation_id: ID of reservation which I want to edit (primary search parameter)
            order_uuid: UUID of reservation which I want to edit (optional if using other search params)
            order_number, serial_number, find_section_id: Alternative search parameters
            find_status: single status to search for (use either find_status or find_statuses, not both)
            find_statuses: list of statuses to search for (use either find_status or find_statuses, not both)
            price: New parameter for price editing
            all other: all other params are optional, if provided, they will be updated

        Returns:
            List[Dict[str, Any]]: List of dictionaries with parameters of edited reservations
        """

        # Validate that at least one search parameter is provided
        search_params = [reservation_id, order_uuid, order_number, serial_number, find_section_id]
        if not any(param is not None for param in search_params):
            self.logger.error("At least one search parameter must be provided")
            return []

        # Validate that at least one update parameter is provided
        update_params = [set_status, set_section_id, insert_pin, package_pin, pickup_pin,
                        card_number, age_control_required, age_controlled, phone_number, price]
        if not any(param is not None for param in update_params):
            self.logger.error("At least one update parameter must be provided")
            return []

        # First, find the reservations using find_reservations function to load parameters
        reservations = await self.find_reservations(
            reservation_id=reservation_id,
            order_uuid=order_uuid,
            order_number=order_number,
            serial_number=serial_number,
            seciton_id=find_section_id,
            status=find_status,
            statuses=find_statuses
        )

        if not reservations:
            self.logger.warning("No reservations found matching search criteria")
            return []

        # Load parameters from the found reservations
        loaded_params = []
        for res in reservations:
            loaded_params.append({
                'reservation_id': res['id'],
                'order_uuid': res.get('order_uuid'),
                'order_number': res.get('order_number'),
                'serial_number': res.get('serial_number'),
                'section_id': res.get('section_id'),
                'status': res.get('status'),
                'insert_pin': res.get('insert_pin'),
                'package_pin': res.get('package_pin'),
                'pickup_pin': res.get('pickup_pin'),
                'card_number': res.get('card_number'),
                'age_control_required': res.get('age_control_required'),
                'age_controlled': res.get('age_controlled'),
                'phone_number': res.get('phone_number'),
                'price': res.get('price')
            })

        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:

            # Build the UPDATE query dynamically based on provided parameters
            update_fields = []
            update_values = []

            if set_status is not None:
                update_fields.append("status = %s")
                update_values.append(set_status)

            if set_section_id is not None:
                update_fields.append("section_id = %s")
                update_values.append(set_section_id)

            if insert_pin is not None:
                update_fields.append("insert_pin = %s")
                update_values.append(insert_pin)

            if package_pin is not None:
                update_fields.append("package_pin = %s")
                update_values.append(package_pin)

            if pickup_pin is not None:
                update_fields.append("pickup_pin = %s")
                update_values.append(pickup_pin)

            if card_number is not None:
                update_fields.append("card_number = %s")
                update_values.append(card_number)

            if age_control_required is not None:
                update_fields.append("age_control_required = %s")
                update_values.append(age_control_required)

            if age_controlled is not None:
                update_fields.append("age_controlled = %s")
                update_values.append(age_controlled)

            if phone_number is not None:
                update_fields.append("phone_number = %s")
                update_values.append(phone_number)

            if price is not None:
                update_fields.append("price = %s")
                update_values.append(float(price))

            # Always update last_update timestamp
            update_fields.append("last_update = NOW()")

            if len(update_fields) == 1:  # Only last_update field
                self.logger.error("No fields provided for update")
                return []

            # Update each reservation using its ID
            updated_reservations = []
            for reservation_data in loaded_params:
                reservation_id = reservation_data['reservation_id']

                # Execute update for this specific reservation
                query = f"UPDATE order_reservations SET {', '.join(update_fields)} WHERE id = %s"
                all_params = update_values + [reservation_id]

                cursor.execute(query, all_params)

                if cursor.rowcount > 0:
                    # Create result data for this updated reservation
                    result_data = reservation_data.copy()  # Start with loaded parameters

                    # Update with new values
                    if set_status is not None:
                        result_data["status"] = set_status
                    if set_section_id is not None:
                        result_data["section_id"] = set_section_id
                    if insert_pin is not None:
                        result_data["insert_pin"] = insert_pin
                    if package_pin is not None:
                        result_data["package_pin"] = package_pin
                    if pickup_pin is not None:
                        result_data["pickup_pin"] = pickup_pin
                    if card_number is not None:
                        result_data["card_number"] = card_number
                    if age_control_required is not None:
                        result_data["age_control_required"] = age_control_required
                    if age_controlled is not None:
                        result_data["age_controlled"] = age_controlled
                    if phone_number is not None:
                        result_data["phone_number"] = phone_number
                    if price is not None:
                        result_data["price"] = float(price)

                    updated_reservations.append(result_data)

            db.commit()

            if not updated_reservations:
                self.logger.error("No rows were updated")
                return []

            self.logger.info(f"Updated {len(updated_reservations)} order reservation(s)")
            return updated_reservations

        except Exception as e:
            db.rollback()
            self.logger.error(f"Error editing order reservations: {e}")
            return []
        finally:
            cursor.close()
            db.close()




    def get_orders_list(self) -> List[Dict[str, Any]]:
        """
        Get all orders with status 7, 8, or 3 grouped by insert_pin.
        Returns list of orders with their packages.
        """
        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            query = """
                SELECT insert_pin, package_pin, section_id, status
                FROM order_reservations
                WHERE status IN (7, 8, 3) AND insert_pin IS NOT NULL
                ORDER BY insert_pin, package_pin
            """
            cursor.execute(query)
            results = cursor.fetchall()

            # Group by insert_pin
            orders_dict = {}
            for row in results:
                insert_pin = row['insert_pin']
                if insert_pin not in orders_dict:
                    orders_dict[insert_pin] = {
                        "insert_pin": insert_pin,
                        "packages": []
                    }

                orders_dict[insert_pin]["packages"].append({
                    "package_pin": row['package_pin'],
                    "section_id": row['section_id'],
                    "status": row['status']
                })

            return list(orders_dict.values())

        except Exception as e:
            self.logger.error(f"Error getting orders list: {e}")
            return []
        finally:
            cursor.close()
            db.close()

    async def find_reservations(
        self,
        reservation_id: Optional[int] = None,
        order_uuid: str = None,
        order_number: str = None,
        serial_number: str = None,
        status: int = None,
        statuses: List[int] = None,
        seciton_id: int = None,
        insert_pin: str = None,
        package_pin: str = None,
        pickup_pin: str = None,
        phone_number: str = None,
        ) -> List[Dict[str, Any]]:

        """
        Function to find records in table order_reservations.

        Params:
            all params are optional, if provided, they will be used to search for reservations
            status: single status to search for (use either status or statuses, not both)
            statuses: list of statuses to search for (use either status or statuses, not both)

        Returns:
            List[Dict[str, Any]]: List of found reservations data, empty list if none found
        """

        db = get_db()
        cursor = db.cursor(dictionary=True)
        try:
            # Build WHERE clause dynamically based on provided parameters
            where_conditions = []
            where_params = []

            if reservation_id is not None:
                where_conditions.append("id = %s")
                where_params.append(reservation_id)

            if order_uuid is not None:
                where_conditions.append("order_uuid = %s")
                where_params.append(order_uuid)

            if order_number is not None:
                where_conditions.append("order_number = %s")
                where_params.append(order_number)

            if serial_number is not None:
                where_conditions.append("serial_number = %s")
                where_params.append(serial_number)

            if status is not None:
                where_conditions.append("status = %s")
                where_params.append(status)

            if statuses is not None and len(statuses) > 0:
                placeholders = ",".join(["%s"] * len(statuses))
                where_conditions.append(f"status IN ({placeholders})")
                where_params.extend(statuses)

            if seciton_id is not None:
                where_conditions.append("section_id = %s")
                where_params.append(seciton_id)

            if insert_pin is not None:
                where_conditions.append("insert_pin = %s")
                where_params.append(insert_pin)

            if package_pin is not None:
                where_conditions.append("package_pin = %s")
                where_params.append(package_pin)

            if pickup_pin is not None:
                where_conditions.append("pickup_pin = %s")
                where_params.append(pickup_pin)

            if phone_number is not None:
                where_conditions.append("phone_number = %s")
                where_params.append(phone_number)

            # If no search criteria provided, return empty list
            if not where_conditions:
                self.logger.warning("No search criteria provided for find_reservations")
                return []

            # Build and execute the SELECT query
            where_clause = " AND ".join(where_conditions)
            query = f"SELECT * FROM order_reservations WHERE {where_clause} ORDER BY created_at DESC"

            cursor.execute(query, where_params)
            results = cursor.fetchall()

            if results:
                self.logger.info(f"Found {len(results)} reservations with provided criteria: {len(where_conditions)} conditions")
                return results
            else:
                self.logger.info(f"No reservations found with provided criteria: {len(where_conditions)} conditions")
                return []

        except Exception as e:
            self.logger.error(f"Error finding order reservations: {e}")
            return []
        finally:
            cursor.close()
            db.close()


    async def select_larger_section(self, previous_section_id: int, package_pin: str = None, insert_pin: str = None) -> int:
        """
        Select larger section than previous_section_id.
        """

        try:

            from infrastructure.repositories.section_repository import section_repository
            sections_info = await section_repository.list_sections()  # get all sections info from database (for now)
            previous_size = None
            reservation = None

            # find out the prevoius section size
            if previous_section_id:
                for section_info in sections_info:
                    if section_info["section_id"] == previous_section_id:
                        previous_size = section_info["size_category"]
                        break
                

            # check if the reservation has reserved section
            if insert_pin:
                if package_pin:
                    reservations = await self.find_reservations(package_pin=package_pin, insert_pin=insert_pin)
                else:
                    reservations = await self.find_reservations(insert_pin=insert_pin)

                if reservations:
                    reservation = reservations[0]
                    # check if reservation have reserved section
                    reserved_section = reservation.get("reserved_section_id")
                    if reserved_section:
                        if reserved_section != previous_section_id:
                            return reserved_section
                    
            if reservation and reservation.get("section_can_change") == 0:
                return None  # return None if section can't change

            # get free sections
            free_sections = sections_info.copy()  # Make a copy to avoid modifying original

            # delete sections from free_sections, in which something already is
            reservations_with_section = await self.find_reservations(statuses=[1, 2, 3, 6, 9])  # get all reservations with section_can_change=0 (for now)
            occupied_section_ids = {reservation.get("section_id") for reservation in reservations_with_section if reservation.get("section_id")}
            free_sections = [section for section in free_sections if section.get("section_id") not in occupied_section_ids]

            # reserve smallest valid sections for reservations, which nothing is not inserted yet
            reservations = await self.find_reservations(statuses=[7, 8])
            sections_to_remove = []

            for reservation in reservations:
                if reservation.get("section_can_change"):
                    valid_section = await self.find_valid_section(free_sections, reservation.get("is_tempered", 0), reservation.get("size_category", 0))
                    if valid_section:
                        sections_to_remove.append(valid_section)
                else:
                    reservation_section_id = reservation.get("section_id")
                    if reservation_section_id:
                        sections_to_remove.append(reservation_section_id)

            # Remove the sections that are now reserved
            free_sections = [section for section in free_sections if section.get("section_id") not in sections_to_remove]


            # finally find valid section for reservation
            if previous_size and reservation:
                valid_section = await self.find_valid_section(free_sections, reservation.get("is_tempered", 0), previous_size + 1)
            elif reservation:
                valid_section = await self.find_valid_section(free_sections, reservation.get("is_tempered", 0), reservation.get("size_category", 0))
            else:
                # No reservation found, return None
                return None

            return valid_section
            

        except Exception as e:
            self.logger.error(f"Error in select_larger_section: {e}")
            return None



    async def find_valid_section(self, free_sections_info: List[Dict[str, Any]], is_tempered: int=0, size_category: int=0, ) -> int:
        """
        Helper function to select section based on criteria.

        Params:
            is_tempered: if section has to be tempered, if this variable is 0, and no nontempered is available, it can return tempered section if available
            size_category: smallest possible size category

        Returns:
            smallest valid section id or None if no valid section found
        """
        try:
            if not free_sections_info:
                return None

            # Filter sections by size category (must be >= required size)
            valid_size_sections = [
                section for section in free_sections_info
                if section.get("size_category", 0) >= size_category
            ]

            if not valid_size_sections:
                return None

            # If tempered is required (is_tempered = 1), filter for tempered sections only
            if is_tempered == 1:
                tempered_sections = [
                    section for section in valid_size_sections
                    if section.get("is_tempered", 0) == 1
                ]
                if tempered_sections:
                    # Return the smallest tempered section (sort by size_category, then by section_id)
                    best_section = min(tempered_sections, key=lambda x: (x.get("size_category", 0), x.get("section_id", 0)))
                    return best_section.get("section_id")
                else:
                    # No tempered sections available
                    return None

            # If tempered is not required (is_tempered = 0), prefer non-tempered but can use tempered
            else:
                # First try to find non-tempered sections
                non_tempered_sections = [
                    section for section in valid_size_sections
                    if section.get("is_tempered", 0) == 0
                ]

                if non_tempered_sections:
                    # Return the smallest non-tempered section
                    best_section = min(non_tempered_sections, key=lambda x: (x.get("size_category", 0), x.get("section_id", 0)))
                    return best_section.get("section_id")
                else:
                    # No non-tempered sections available, use tempered as fallback
                    tempered_sections = [
                        section for section in valid_size_sections
                        if section.get("is_tempered", 0) == 1
                    ]
                    if tempered_sections:
                        best_section = min(tempered_sections, key=lambda x: (x.get("size_category", 0), x.get("section_id", 0)))
                        return best_section.get("section_id")
                    else:
                        return None

        except Exception as e:
            self.logger.error(f"Error in find_valid_section: {e}")
            return None

    async def order_status_change(self, status: int, order_number: str) -> bool:
        """
        Call external API to change order status.

        Args:
            status: New status to set
            order_number: Order number to change status for

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from infrastructure.external_apis.jetveo_client import jetveo_client

            # Get order_uuid from the first reservation with this order_number
            reservations = await self.find_reservations(order_number=order_number)
            if not reservations:
                self.logger.warning(f"No reservations found for order_number: {order_number}")
                return False

            order_uuid = reservations[0].get('order_uuid')
            if not order_uuid:
                self.logger.warning(f"No order_uuid found for order_number: {order_number}")
                return False

            # Call external API to change order status
            # action=1 seems to be used for status changes based on existing code
            success = await jetveo_client.order_change_status(
                action=1,
                status=status,
                order_number=order_number,
                order_uuid=order_uuid
            )

            if success:
                self.logger.info(f"Successfully changed order status to {status} for order_number: {order_number}")
            else:
                self.logger.warning(f"Failed to change order status to {status} for order_number: {order_number}")

            return success

        except Exception as e:
            self.logger.error(f"Error in order_status_change: {e}")
            return False

    async def check_after_delivery(self) -> Dict[str, Any]:
        """
        Check if all packages for each order were delivered.
        Process orders with status 3 and complete orders where all packages have status 3.

        Returns:
            Dict with success status, message, and sections for pickup if needed
        """
        try:
            # Get all reservations with status 3 (delivered)
            delivered_reservations = await self.find_reservations(status=3)

            if not delivered_reservations:
                return {
                    "success": True,
                    "message": "No delivered orders to check",
                    "sections": [],
                    "total_sections": 0,
                    "needs_pickup": False
                }

            # Group by insert_pin
            orders_by_insert_pin = {}
            for reservation in delivered_reservations:
                insert_pin = reservation.get('insert_pin')
                if insert_pin:
                    if insert_pin not in orders_by_insert_pin:
                        orders_by_insert_pin[insert_pin] = []
                    orders_by_insert_pin[insert_pin].append(reservation)

            completed_orders = []
            remaining_status_3_sections = []

            # Check each order (insert_pin group)
            for insert_pin, delivered_packages in orders_by_insert_pin.items():
                # Get all reservations with this insert_pin and status 7, 8, or 3
                all_order_reservations = await self.find_reservations(insert_pin=insert_pin, statuses=[7, 8, 3])

                # Check if all packages have status 3
                all_delivered = all(r.get('status') == 3 for r in all_order_reservations)

                if all_delivered:
                    # Complete the order - change all status 3 to status 2 for valid sections
                    for reservation in all_order_reservations:
                        await self.edit_reservations(
                            reservation_id=reservation.get('id'),
                            set_status=2
                        )

                    # After changing all reservations for the same insert_pin, call order_status_change
                    # Get order_number from the first reservation (all reservations with same insert_pin have same order_number)
                    order_number = all_order_reservations[0].get('order_number')
                    if order_number:
                        await self.order_status_change(status=2, order_number=order_number)
                    else:
                        self.logger.warning(f"No order_number found for insert_pin: {insert_pin}")

                    completed_orders.append(insert_pin)
                    self.logger.info(f"Completed order with insert_pin: {insert_pin}")
                else:
                    # Order not complete, keep status 3 reservations for pickup
                    for reservation in delivered_packages:
                        section_id = reservation.get('section_id')
                        if section_id:
                            remaining_status_3_sections.append(section_id)

            # Remove duplicates from sections
            remaining_status_3_sections = list(set(remaining_status_3_sections))

            if not remaining_status_3_sections:
                return {
                    "success": True,
                    "message": f"All orders completed. Processed {len(completed_orders)} complete orders.",
                    "sections": [],
                    "total_sections": 0,
                    "needs_pickup": False,
                    "completed_orders": len(completed_orders)
                }
            else:
                return {
                    "success": True,
                    "message": f"Found {len(remaining_status_3_sections)} sections with incomplete orders that need pickup",
                    "sections": remaining_status_3_sections,
                    "total_sections": len(remaining_status_3_sections),
                    "needs_pickup": True,
                    "completed_orders": len(completed_orders)
                }

        except Exception as e:
            self.logger.error(f"Error in check_after_delivery: {e}")
            return {
                "success": False,
                "message": f"Internal error: {str(e)}",
                "sections": [],
                "total_sections": 0,
                "needs_pickup": False
            }



# Global repository instance
order_repository = OrderRepository()
