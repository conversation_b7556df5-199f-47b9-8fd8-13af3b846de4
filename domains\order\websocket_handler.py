"""
WebSocket handler for order operations.
Uses universal pickup_process() and select_sections() functions for all operations.
"""

import json
import asyncio
import random
from fastapi import WebSocket, WebSocketDisconnect
from managers import ws_manager, session_manager
from managers.logger_manager import logger
# OrderRepository import removed - reservations now created via /order/create_reservation endpoint
from infrastructure.repositories.section_repository import section_repository


async def handle_order_websocket(websocket: WebSocket, session_id: str):
    """
    Universal WebSocket handler for all order operations.
    Routes to pickup_process() or select_sections() based on operation type.
    """
    logger.info(f"Order WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Order WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        # Get operation details from session
        operation = getattr(session, 'operation', 'unknown')
        logger.info(f"Order operation: {operation}")

        # Route to appropriate handler based on operation type
        if operation in ["pickup_expired", "pickup_employee", "customer_pickup", "check_after_delivery_pickup"]:
            await _handle_pickup_operation(websocket, session_id, session, operation)
        elif operation == "operator_deliver":
            await _handle_operator_deliver_operation(websocket, session_id, session)
        elif operation in ["deliver_employee", "employee_send", "customer_reclaim", "customer_send", "deliver_order"]:
            await _handle_selection_operation(websocket, session_id, session, operation)
        else:
            logger.error(f"Unknown operation: {operation}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Unknown operation: {operation}"
            })

    except Exception as e:
        logger.error(f"Error in order WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        logger.info(f"Order WebSocket handler ended for session: {session_id}")


async def _handle_pickup_operation(websocket: WebSocket, session_id: str, session, operation: str):
    """Handle pickup operations using pickup_process()"""
    logger.info(f"Handling pickup operation: {operation}")

    # Get sections and operation details from session
    sections = getattr(session, 'sections', None)  # This field exists in SessionData
    endpoint_type = getattr(session, 'endpoint_type', 'unknown')

    if not sections:
        logger.error(f"No sections found in session {session_id}")
        await ws_manager.send(session_id, {
            "type": "error",
            "message": "No sections found"
        })
        return

    # Create message queue for pickup_process
    message_queue = asyncio.Queue()

    # Message handler to route WebSocket messages to pickup_process
    async def handle_websocket_messages():
        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                if not message or message.strip() == "":
                    continue

                try:
                    data = json.loads(message)
                except json.JSONDecodeError:
                    await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                    continue

                msg_type = data.get("type")

                if msg_type == "ping":
                    await ws_manager.send(session_id, {"type": "pong"})
                    continue

                # Route message to pickup_process
                await message_queue.put(data)

            except WebSocketDisconnect:
                logger.info(f"Order pickup WebSocket disconnected: {session_id}")
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                break

    # Start message handler task
    message_task = asyncio.create_task(handle_websocket_messages())

    # Age check for customer pickup
    if endpoint_type == "order/customer/pickup":
        age_control_required =  getattr(session, 'age_control_required', False)
        age_controlled = getattr(session, 'age_controlled', False)
        if age_control_required and not age_controlled:
            from managers.process_manager import age_check_process
            success = await age_check_process(session_id, message_queue)
            if not success:
                message_task.cancel()
                return
            session_manager.update_session(session_id, age_controlled=True)

            # Edit reservation
            from infrastructure.repositories.order_repository import order_repository
            await order_repository.edit_reservations(
                order_number=getattr(session, 'order_number', None),
                age_controlled=1
            )

    # Start pickup_process from universal process_manager (orders don't require payment)
    from managers.process_manager import pickup_process
    success, _ = await pickup_process(
        sections=sections,
        session_id=session_id,
        message_queue=message_queue
    )

    # Cancel message handler
    message_task.cancel()


    logger.info(f"Order pickup completed for session {session_id}: success={success}")


async def _handle_selection_operation(websocket: WebSocket, session_id: str, session, operation: str):
    """Handle selection operations using select_sections()"""
    logger.info(f"Handling selection operation: {operation}")

    # Get operation details from session
    user_data = session.user_data or {}
    phone_number = user_data.get('phone_number', None)
    reservation_pin = user_data.get('reservation_pin', None)
    reserved_section_id = user_data.get('reserved_section_id', None)
    reserved_section_ids = user_data.get('reserved_section_ids', [])
    available_sections = section_repository.get_available_sections("order")

    logger.info(f"Operation: {operation}, reserved_section_id: {reserved_section_id}, reserved_section_ids: {reserved_section_ids}")

    # Determine available sections based on operation
    if operation == "deliver_employee" and reserved_section_ids:
        # Courier delivering to employee - use reserved sections
        available_sections = reserved_section_ids
        reserved_sections = reserved_section_ids
    elif operation in ["employee_send", "customer_reclaim", "customer_send"] and reserved_section_id:
        # Single section operations - use reserved section
        available_sections = [reserved_section_id]
        reserved_sections = [reserved_section_id]
    elif operation in ["employee_send", "customer_reclaim", "customer_send"] and reserved_section_ids:
        # Multiple section operations - use reserved sections
        available_sections = reserved_section_ids
        reserved_sections = reserved_section_ids
    else:
        # Get all available sections (fallback)
        available_sections = section_repository.get_available_sections("order")
        reserved_sections = available_sections

    logger.info(f"Final sections - available: {available_sections}, reserved: {reserved_sections}")

    # Create message queue for select_sections
    message_queue = asyncio.Queue()

    # Message handler to route WebSocket messages to select_sections
    async def handle_websocket_messages():
        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                if not message or message.strip() == "":
                    continue

                try:
                    data = json.loads(message)
                except json.JSONDecodeError:
                    await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                    continue

                msg_type = data.get("type")

                if msg_type == "ping":
                    await ws_manager.send(session_id, {"type": "pong"})
                    continue

                # Route message to select_sections
                await message_queue.put(data)

            except WebSocketDisconnect:
                logger.info(f"Order selection WebSocket disconnected: {session_id}")
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                break

    # Start message handler task
    message_task = asyncio.create_task(handle_websocket_messages())

    # Start select_sections from universal process_manager
    from managers.process_manager import select_sections
    success, successful_sections = await select_sections(
        reserved_sections=reserved_sections,
        available_sections=available_sections,
        session_id=session_id,
        message_queue=message_queue
    )

    # Cancel message handler
    message_task.cancel()

    # Order selection completed - reservations will be created via /order/create_reservation endpoint
    # The frontend should call /order/create_reservation with session_id and successful_sections

    logger.info(f"Order selection completed for session {session_id}: success={success}")


async def _handle_operator_deliver_operation(websocket: WebSocket, session_id: str, session):
    """Handle operator deliver operation - wait for order scans and process them"""
    logger.info(f"Handling operator deliver operation for session {session_id}")

    # Import order repository
    from infrastructure.repositories.order_repository import order_repository

    # Create message queue for WebSocket messages
    message_queue = asyncio.Queue()

    # Send initial waiting_for_order message
    await ws_manager.send(session_id, {
        "type": "waiting_for_order",
        "message": "Waiting for order scan"
    })

    # Message handler to process WebSocket messages
    async def handle_websocket_messages():
        while ws_manager.is_connected(session_id):
            try:
                message = await websocket.receive_text()
                if not message or message.strip() == "":
                    continue

                try:
                    data = json.loads(message)
                except json.JSONDecodeError:
                    await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                    continue

                msg_type = data.get("type")

                if msg_type == "ping":
                    await ws_manager.send(session_id, {"type": "pong"})
                    continue

                # Route message to main handler
                await message_queue.put(data)

            except WebSocketDisconnect:
                logger.info(f"Operator deliver WebSocket disconnected: {session_id}")
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                break

    # Start message handler task
    message_task = asyncio.create_task(handle_websocket_messages())

    # Main processing loop
    try:
        while ws_manager.is_connected(session_id):
            
            await ws_manager.send(session_id, {
                "type": "waiting_for_order",
                "message": "Waiting for next order scan"
            })
            
            try:
                # Wait for message from websocket
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message: {e}")
                break

            message_type = message.get("type")
            logger.info(f"Processing operator deliver message: {message_type}")

            if message_type == "order_scanned":
                package_pin = message.get("package_pin")
                insert_pin = message.get("insert_pin")

                if package_pin:
                    # Handle package_pin scan
                    logger.info(f"Package PIN scanned: {package_pin}")

                    # Find reservation with this package_pin and status 7 or 8, there is always only one
                    valid_reservations = await order_repository.find_reservations(package_pin=package_pin, statuses=[7, 8])

                    if not valid_reservations:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": f"No valid order found for package PIN: {package_pin}"
                        })
                        continue

                    # Get the first valid reservation
                    reservation = valid_reservations[0]
                    section_id = reservation.get('section_id')

                    if not section_id:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "No section assigned to this order"
                        })
                        continue

                    # Call select_sections for this specific section
                    from managers.process_manager import select_sections
                    success, selected_sections = await select_sections(
                        reserved_sections=[section_id],
                        available_sections=[section_id],
                        session_id=session_id,
                        message_queue=message_queue,
                        package_pin=package_pin,
                        insert_pin=insert_pin
                    )

                    if success and selected_sections:
                        # Change status to 3
                        await order_repository.edit_reservations(
                            package_pin=package_pin,
                            find_status=reservation.get('status'),
                            set_status=3
                        )

                        await ws_manager.send(session_id, {
                            "type": "order_processed",
                            "message": f"Order with package PIN {package_pin} processed successfully",
                            "package_pin": package_pin,
                            "section_id": section_id
                        })
                    else:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "Section selection failed"
                        })

                elif insert_pin:
                    # Handle insert_pin scan
                    logger.info(f"Insert PIN scanned: {insert_pin}")

                    # Find reservations with this insert_pin and status 7 or 8
                    valid_reservations = await order_repository.find_reservations(insert_pin=insert_pin, statuses=[7, 8])

                    if not valid_reservations:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": f"No valid orders found for insert PIN: {insert_pin}"
                        })
                        continue

                    # Pick a random reservation from valid ones
                    import random
                    reservation = random.choice(valid_reservations)

                    # Change status to 3
                    await order_repository.edit_reservations(
                        reservation_id=reservation.get('id'),
                        set_status=3
                    )

                    await ws_manager.send(session_id, {
                        "type": "order_processed",
                        "message": f"Order with insert PIN {insert_pin} processed successfully",
                        "insert_pin": insert_pin,
                        "reservation_id": reservation.get('id')
                    })

                else:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Either package_pin or insert_pin must be provided"
                    })

            # End deliver operation
            elif message_type == "end_deliver":
                await ws_manager.send(session_id, {"type": "delivery_completed"})


    except Exception as e:
        logger.error(f"Error in operator deliver handler: {e}")
        await ws_manager.send(session_id, {
            "type": "error",
            "message": f"Internal error: {str(e)}"
        })
    finally:
        # Cancel message handler
        message_task.cancel()
        logger.info(f"Operator deliver operation completed for session {session_id}")

