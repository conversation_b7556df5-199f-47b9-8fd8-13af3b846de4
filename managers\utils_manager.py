"""
Utils Manager - Utility functions for various operations

Contains utility functions for:
- Environment variable management
- Provision data handling
- File operations
"""

import os
import re
from managers.logger_manager import logger
from infrastructure.external_apis.jetveo_client import jetveo_client


async def update_provision_in_env() -> bool:
    """
    Fetch provision data from server and update SERIAL_NUMBER in .env file.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("Updating provision data in .env file")

        # Call provision_get() from jetveo_client
        provision_data = await jetveo_client.provision_get()
        if not provision_data:
            logger.error("Failed to get provision data from server")
            return False
        logger.info(f"Provision data received from server: {provision_data}")

        # Extract serialNumber from the response
        result = provision_data.get('result', {})

        variables_to_env = {
            "SERIAL_NUMBER": result.get('provision').get('serialNumber'),

            "MQTT_PASSWORD": result.get('mqtt').get('clientPassword'),

            # ? global codes jak ? 
            "GS_MANAGER_PIN":   result.get("access").get('managerCode'),
            "GS_SERVICE_PIN":   result.get("access").get('serviceCode'),
            "GS_INSTALLATION_PIN": result.get("access").get('montageCode'),
            "GC_MANAGER_PIN":   result.get("access").get("globalCodes").get('managerCode'),
            "GC_SERVICE_PIN":   result.get("access").get("globalCodes").get('serviceCode'),
            
            "FEATURE_STORAGE": result.get("backend").get('storage'),
            "FEATURE_ORDERS": result.get("backend").get('order'),
            "FEATURE_SALE": result.get("backend").get('sale'),
            
            "HARDWARE_VERSION": result.get("electronic").get("scriptVersion"),
            # "SCRIPT_PATH": result.get("scriptPath"),
            "HARDWARE_TYPE": result.get("electronic").get('scriptType'),
            "TEMPERATURE_ELECTRONIC_ENABLED": result.get("electronic").get('electronic'),
            "TEMPERATURE_COMET_ENABLED": result.get("electronic").get('comet'),
        }

        # module
        for mode, enabled in result.get("backend", {}).items():
            if enabled is True:
                variables_to_env.update({"BOX_TYPE": mode})

        # temperature sensors
        comet_sensors = result.get("electronic", {}).get("cometSensors", [])
        electronic_sensors = result.get("electronic", {}).get("electronicSensors", [])

        # Convert sensor arrays to comma-separated strings
        if electronic_sensors and isinstance(electronic_sensors, list):
            temperature_sensor_pins = ",".join(str(sensor) for sensor in electronic_sensors)
            variables_to_env["TEMPERATURE_SENSOR_PINS"] = temperature_sensor_pins
            logger.info(f"Set TEMPERATURE_SENSOR_PINS: {temperature_sensor_pins}")

        if comet_sensors and isinstance(comet_sensors, list):
            comet_ip_addresses = ",".join(str(sensor) for sensor in comet_sensors)
            variables_to_env["COMET_IP_ADDRESSES"] = comet_ip_addresses
            logger.info(f"Set COMET_IP_ADDRESSES: {comet_ip_addresses}")



        # Update .env file with the serial number
        for key, value in variables_to_env.items():
            if value:
                success = _update_env_variable(key, value)
                if not success:
                    logger.error(f"Failed to update {key} in .env file")
                    return False
            else:
                logger.warning(f"Value for {key} not found in provision data")

        return True


    except Exception as e:
        logger.error(f"Error updating serial number from provision: {e}")
        return False




def _update_env_variable(key: str, value: str) -> bool:
    """
    Update or add an environment variable in the .env file.

    Args:
        key: Environment variable name
        value: Environment variable value

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        env_file_path = '.env'

        # Read existing .env file content
        if os.path.exists(env_file_path):
            with open(env_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        else:
            lines = []

        # Find if the key already exists
        key_pattern = re.compile(f'^{re.escape(key)}=.*$')
        key_found = False

        # Update existing key or mark for addition
        for i, line in enumerate(lines):
            if key_pattern.match(line.strip()):
                lines[i] = f'{key}={value}\n'
                key_found = True
                break

        # Add new key if not found
        if not key_found:
            # Ensure file ends with newline before adding new variable
            if lines and not lines[-1].endswith('\n'):
                lines[-1] += '\n'
            lines.append(f'{key}={value}\n')

        # Write updated content back to .env file
        with open(env_file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)

        return True

    except Exception as e:
        logger.error(f"Error updating .env file: {e}")
        return False